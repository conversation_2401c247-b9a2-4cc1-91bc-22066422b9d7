import axios from 'axios'
import { useLanguageStore } from '../stores/language.js'

// 创建axios实例
const request = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL || '/',
  timeout: 10000
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 添加Accept-Language请求头
    const languageStore = useLanguageStore()
    if (languageStore.currentLang) {
      config.headers['Accept-Language'] = languageStore.currentLang === 'zh' ? 'zh-CN' : 'en-US'
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 修正：直接返回完整的响应体，让调用方处理
    return response.data
  },
  error => {
    console.error('Request Error:', error.message)
    return Promise.reject(error)
  }
)

export default request
