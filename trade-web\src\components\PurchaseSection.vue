<template>
  <section class="about-section">
    <div class="container">
      <div class="section-header">
        <h2 class="section-title">{{ currentLang === 'zh' ? '关于我们' : 'About Us' }}</h2>
        <p class="section-subtitle">{{ currentLang === 'zh' ? '华南理工大学广州校友会企业出海服务分会' : 'SCUT Guangzhou Alumni Association - Enterprise Overseas Service Branch' }}</p>
      </div>
      
      <div class="about-content">
        <div class="about-left">
          <div class="about-card">
            <div class="about-icon">
              <el-icon size="large"><Document /></el-icon>
            </div>
            <h3 class="about-card-title">{{ currentLang === 'zh' ? '我们的使命' : 'Our Mission' }}</h3>
            <p class="about-description">
              {{ currentLang === 'zh' ? '华南理工大学广州校友会企业出海服务分会致力于为校友企业提供国际化发展支持，打造交流合作平台，促进企业走向全球市场。' : 'The SCUT Guangzhou Alumni Association Enterprise Overseas Service Branch is committed to providing internationalization support for alumni enterprises, creating a platform for exchange and cooperation, and promoting enterprises to go global.' }}
            </p>
          </div>

          <div class="about-features">
            <div class="feature-item" v-for="feature in aboutFeatures" :key="feature.id">
              <div class="feature-icon">
                <el-icon>
                  <component :is="feature.icon" />
                </el-icon>
              </div>
              <div class="feature-content">
                <h3>{{ currentLang === 'zh' ? feature.title : feature.titleEn }}</h3>
                <p>{{ currentLang === 'zh' ? feature.desc : feature.descEn }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="about-right">
          <div class="stats-grid">
            <div class="stat-item" v-for="stat in companyStats" :key="stat.id">
              <div class="stat-number">{{ stat.number }}</div>
              <div class="stat-label">{{ currentLang === 'zh' ? stat.label : stat.labelEn }}</div>
            </div>
          </div>
          
          <div class="contact-card">
            <h3 class="contact-title">{{ currentLang === 'zh' ? '联系方式' : 'Contact Us' }}</h3>
            <div class="contact-info">
              <div class="contact-row">
                <el-icon><Location /></el-icon>
                <span>{{ currentLang === 'zh' ? '广州市黄埔区凤凰三横路39号创尔生物' : 'South China University of Technology, Tianhe District, Guangzhou' }}</span>
              </div>
              <div class="contact-row">
                <el-icon><Phone /></el-icon>
                <span>8615915732990</span>
              </div>
              <div class="contact-row">
                <el-icon><Message /></el-icon>
                <span><EMAIL></span>
              </div>
            </div>
            
            <div class="about-actions">
              <el-button type="primary" class="contact-btn">
                {{ currentLang === 'zh' ? '联系我们' : 'Contact Us' }}
              </el-button>
              <el-button class="join-btn">
                {{ currentLang === 'zh' ? '加入我们' : 'Join Us' }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script>
import { computed } from 'vue'
import { useLanguageStore } from '../stores/language.js'
import { Connection, Promotion, Suitcase, Document, Location, Phone, Message } from '@element-plus/icons-vue'

export default {
  name: 'AboutSection',
  setup() {
    const languageStore = useLanguageStore()
    const currentLang = computed(() => languageStore.currentLang)
    
    const aboutFeatures = [
      {
        id: 1,
        icon: 'Connection',
        title: '校友网络',
        titleEn: 'Alumni Network',
        desc: '连接广泛的华工校友企业资源',
        descEn: 'Connect with extensive SCUT alumni enterprise resources'
      },
      {
        id: 2,
        icon: 'Promotion',
        title: '国际推广',
        titleEn: 'Global Promotion',
        desc: '助力企业产品与服务拓展国际市场',
        descEn: 'Help enterprises expand products and services to international markets'
      },
      {
        id: 3,
        icon: 'Suitcase',
        title: '专业指导',
        titleEn: 'Professional Guidance',
        desc: '提供出海战略和本地化解决方案',
        descEn: 'Provide overseas strategies and localization solutions'
      }
    ]
    
    const companyStats = [
      {
        id: 1,
        number: '15+',
        label: '服务年限',
        labelEn: 'Years of Service'
      },
      {
        id: 2,
        number: '300+',
        label: '会员企业',
        labelEn: 'Member Enterprises'
      },
      {
        id: 3,
        number: '50+',
        label: '合作国家',
        labelEn: 'Partner Countries'
      },
      {
        id: 4,
        number: '1000+',
        label: '成功案例',
        labelEn: 'Success Stories'
      }
    ]
    
    return {
      currentLang,
      aboutFeatures,
      companyStats,
      Connection,
      Promotion,
      Suitcase,
      Document,
      Location,
      Phone,
      Message
    }
  }
}
</script>

<style lang="scss" scoped>
.about-section {
  padding: 40px 0;
  background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .section-header {
    text-align: center;
    margin-bottom: 30px;
    
    .section-title {
      font-size: 32px;
      font-weight: 700;
      color: #1e3a8a;
      margin: 0 0 10px;
    }
    
    .section-subtitle {
      font-size: 16px;
      color: #64748b;
      margin: 0;
    }
  }
  
  .about-content {
    display: flex;
    gap: 30px;
    align-items: stretch; // 确保左右两侧高度一致
  }
  
  .about-left {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
    
    .about-card {
      background: white;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      text-align: left;
      
      .about-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        border-radius: 10px;
        background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
        color: #2563eb;
        margin-bottom: 15px;
      }
      
      .about-card-title {
        font-size: 18px;
        font-weight: 600;
        color: #1e3a8a;
        margin: 0 0 12px 0;
      }
      
      .about-description {
        font-size: 15px;
        color: #4b5563;
        line-height: 1.6;
        margin: 0;
      }
    }
    
    .about-features {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 16px;
      flex: 1; // 占据剩余空间
      
      .feature-item {
        display: flex;
        gap: 16px;
        padding: 20px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
          flex-shrink: 0;
          width: 45px;
          height: 45px;
          background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 20px;
        }
        
        .feature-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          
          h3 {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin: 0 0 8px 0;
          }
          
          p {
            font-size: 14px;
            color: #6b7280;
            margin: 0;
            line-height: 1.5;
          }
        }
      }
    }
  }
  
  .about-right {
    width: 40%;
    display: flex;
    flex-direction: column;
    gap: 20px;
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 16px;
      
      .stat-item {
        text-align: center;
        padding: 20px 15px;
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        display: flex;
        flex-direction: column;
        justify-content: center;
        
        &:hover {
          transform: translateY(-3px);
          box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        .stat-number {
          font-size: 28px;
          font-weight: 700;
          color: #2563eb;
          margin-bottom: 5px;
          line-height: 1;
        }
        
        .stat-label {
          font-size: 13px;
          color: #6b7280;
          font-weight: 500;
        }
      }
    }
    
    .contact-card {
      background: white;
      padding: 24px;
      border-radius: 12px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
      flex: 1;
      display: flex;
      flex-direction: column;
      
      .contact-title {
        font-size: 18px;
        font-weight: 600;
        color: #1e3a8a;
        margin: 0 0 20px 0;
      }
      
      .contact-info {
        margin-bottom: 20px;
        flex: 1;
        
        .contact-row {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 15px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .el-icon {
            font-size: 16px;
            color: #2563eb;
          }
          
          span {
            font-size: 14px;
            color: #4b5563;
          }
        }
      }
    }
    
    .about-actions {
      display: flex;
      gap: 12px;
      margin-top: auto;
      
      .contact-btn,
      .join-btn {
        flex: 1;
        height: 40px;
        font-size: 14px;
        font-weight: 600;
        border-radius: 8px;
      }
      
      .contact-btn {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
        border: none;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        }
      }
      
      .join-btn {
        border: 1px solid #2563eb;
        color: #2563eb;
        
        &:hover {
          background: rgba(37, 99, 235, 0.05);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .about-section {
    padding: 30px 0;
    
    .about-content {
      flex-direction: column;
      gap: 30px;
    }
    
    .about-right {
      width: 100%;
    }
  }
}

@media (max-width: 576px) {
  .about-section {
    .stats-grid {
      gap: 12px !important;
      
      .stat-item {
        padding: 15px 10px !important;
        
        .stat-number {
          font-size: 22px !important;
        }
        
        .stat-label {
          font-size: 12px !important;
        }
      }
    }
  }
}
</style> 